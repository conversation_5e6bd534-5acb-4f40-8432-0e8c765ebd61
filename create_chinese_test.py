#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建包含中文內容的測試 PDF
"""

import fitz  # PyMuPDF


def create_chinese_test_pdf():
    """創建包含中文內容的測試 PDF"""
    
    # 創建新的 PDF 文檔
    doc = fitz.open()
    page = doc.new_page()
    
    # 中英文混合測試內容
    chinese_text = """繁體中文測試文檔

這是一個測試 PDF 文檔，用於驗證繁體中文 OCR 功能。

主要功能：
• PDF 文檔處理
• 圖像預處理
• 光學字符識別
• 文字提取與儲存

English Content Test

This document contains both Traditional Chinese and English text.

Features:
• PDF document processing
• Image preprocessing  
• Optical Character Recognition
• Text extraction and storage

測試數字：12345
測試符號：！@#$%
測試日期：2025年1月8日

混合內容測試：
Hello 你好 World 世界
Python OCR 光學字符識別
PDF 處理程式 Processing Program
    """
    
    try:
        # 插入文字（使用較大的字體以提高 OCR 準確性）
        page.insert_text((50, 50), chinese_text, fontsize=14)
        
        # 儲存 PDF
        doc.save("chinese_test.pdf")
        doc.close()
        
        print("✓ 中文測試 PDF 創建成功：chinese_test.pdf")
        return True
        
    except Exception as e:
        print(f"✗ 創建中文測試 PDF 失敗：{str(e)}")
        return False


if __name__ == "__main__":
    create_chinese_test_pdf()
