# PDF OCR 處理程式

這是一個使用 Python 開發的 PDF OCR 處理程式，可以將 PDF 文檔中的中文和英文文字提取出來。

## 功能特色

- 使用 **PyMuPDF (fitz)** 將 PDF 頁面轉換為高品質圖像
- 使用 **Pillow (PIL)** 進行圖像處理
- 使用 **pytesseract** 進行 OCR 文字識別，支援繁體中文和英文
- 使用 **OpenCV** 進行圖像預處理，提高 OCR 準確性

## 系統需求

### Python 套件
- PyMuPDF (fitz)
- Pillow (PIL)
- pytesseract
- OpenCV
- numpy

### 外部軟體
- **Tesseract OCR** - 需要單獨安裝

## 安裝步驟

### 1. 建立虛擬環境
```bash
py -m venv pdf_ocr_env
pdf_ocr_env\Scripts\activate
```

### 2. 安裝 Python 套件
```bash
pip install -r requirements.txt
```

### 3. 安裝 Tesseract OCR

#### Windows 系統：
1. 下載 Tesseract OCR：https://github.com/UB-Mannheim/tesseract/wiki
2. 安裝到預設路徑（通常是 `C:\Program Files\Tesseract-OCR\`）
3. 確保安裝時選擇了繁體中文語言包

#### 驗證安裝：
```bash
tesseract --version
tesseract --list-langs
```
應該能看到 `chi_tra`（繁體中文）在語言列表中。

## 使用方法

### 基本用法
```bash
python pdf_ocr.py <PDF文件路徑> [輸出文字文件路徑]
```

### 範例
```bash
# 處理 PDF 並自動生成輸出文件
python pdf_ocr.py document.pdf

# 處理 PDF 並指定輸出文件名稱
python pdf_ocr.py document.pdf extracted_text.txt
```

## 程式特色

### 1. PDF 轉圖像
- 使用 PyMuPDF 將每一頁轉換為高解析度圖像（預設 300 DPI）
- 支援多頁 PDF 文檔

### 2. 圖像預處理
- 轉換為灰階圖像
- 降噪處理
- 自動二值化，提高 OCR 準確性

### 3. OCR 處理
- 支援繁體中文和英文混合識別
- 可自訂語言設定
- 自動偵測 Tesseract 安裝路徑

### 4. 輸出格式
- 按頁面分段顯示結果
- 自動儲存為 UTF-8 編碼的文字文件
- 在終端顯示處理進度和預覽

## 程式架構

```
PDFOCRProcessor 類別
├── __init__()          # 初始化，設定 Tesseract 路徑
├── pdf_to_images()     # PDF 轉圖像
├── preprocess_image()  # 圖像預處理
├── perform_ocr()       # 執行 OCR
└── process_pdf()       # 完整處理流程
```

## 故障排除

### 1. Tesseract 找不到
如果出現 "tesseract is not installed" 錯誤：
- 確認 Tesseract 已正確安裝
- 檢查程式是否能自動偵測到 Tesseract 路徑
- 手動指定路徑：
```python
processor = PDFOCRProcessor(tesseract_path=r'C:\Program Files\Tesseract-OCR\tesseract.exe')
```

### 2. 中文識別不準確
- 確認安裝了繁體中文語言包
- 嘗試調整 DPI 設定（提高解析度）
- 檢查原始 PDF 的圖像品質

### 3. 記憶體不足
- 處理大型 PDF 時可能需要較多記憶體
- 可以考慮分批處理或降低 DPI

## 自訂設定

您可以修改以下參數來優化結果：

```python
# 調整解析度（更高的 DPI = 更好的品質，但處理時間更長）
text = processor.process_pdf(pdf_path, dpi=600)

# 變更 OCR 語言設定
text = processor.process_pdf(pdf_path, lang='chi_sim+eng')  # 簡體中文+英文
```

## 授權

此程式僅供學習和個人使用。
