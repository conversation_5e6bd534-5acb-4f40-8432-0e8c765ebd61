cv2/Error/__init__.pyi,sha256=vQNfAGSIi0Hs_kO9KFV3zZv920KEBDcZpdOFnmkZkDg,4194
cv2/LICENSE-3RD-PARTY.txt,sha256=2OyIgyD8udmTF6d69KSjqRIIZ2Bn7B-pvBlnpSJBFzA,177945
cv2/LICENSE.txt,sha256=7e8PrB6wjSnTRWP3JHQuB42iUT4ZYTOhLGrZ_wHiYQc,1090
cv2/__init__.py,sha256=lXqRv9mP-wehDNeJt8XEaAZWhHa2HjTHrVagAJK5gaU,6793
cv2/__init__.pyi,sha256=ml9KQgsVDkeG0zj6-vJv0LJWsOzUQA9Ks9ZUIOVbfAA,315561
cv2/__pycache__/__init__.cpython-312.pyc,,
cv2/__pycache__/config-3.cpython-312.pyc,,
cv2/__pycache__/config.cpython-312.pyc,,
cv2/__pycache__/load_config_py2.cpython-312.pyc,,
cv2/__pycache__/load_config_py3.cpython-312.pyc,,
cv2/__pycache__/version.cpython-312.pyc,,
cv2/aruco/__init__.pyi,sha256=xbcA4yvMYM5KraXaGdLCQe6h3XPtShS-zT-tHe4u8gQ,15465
cv2/barcode/__init__.pyi,sha256=-tWHNaRNKDmy741nNDClIeTepyLgToWbbifRL9F2668,1480
cv2/config-3.py,sha256=3ijHtSE8yhSPCUaZFlhGEbPWbByMQyiAJZ1qOpI4AhM,748
cv2/config.py,sha256=bGNnq85VmVrkqNPBmUIOxTr6sofLF9_C9DA3ivdd0wE,123
cv2/cuda/__init__.pyi,sha256=meRGvrVEmx91nv7VL-X9xRnco7wMKK39zEZpgbZc5Gc,16718
cv2/cv2.pyd,sha256=ITpbWF-AQ86NhAY43G5vqwJQy4aXON7qBNoAarZd-OQ,70967296
cv2/data/__init__.py,sha256=9M4Wch7X9iOk3MRDumANGFbbYQyyw9U8E6jKAozGj20,73
cv2/data/__pycache__/__init__.cpython-312.pyc,,
cv2/data/haarcascade_eye.xml,sha256=ccxk_DBaNV3GAGeID2-71D3RVb1j7jhEZhob2jSy_Yw,341406
cv2/data/haarcascade_eye_tree_eyeglasses.xml,sha256=4y-cZ5NcM-nRMx6xT6WFVP8Xg1wDdCZjvLl6iS6Talc,601661
cv2/data/haarcascade_frontalcatface.xml,sha256=rCusk07yQoTviisunY5X7vhKwdaUO00R5cnoWE3Aacg,411388
cv2/data/haarcascade_frontalcatface_extended.xml,sha256=_9DR0o8H0DdsidtMmEUAnChVzHbIz_dj1TMdyTYdqFQ,382918
cv2/data/haarcascade_frontalface_alt.xml,sha256=YoHfE0Wcwhj_BH0Csq44WbEv8UqT_-iVL3sz-te5aXs,676709
cv2/data/haarcascade_frontalface_alt2.xml,sha256=ewyWfZq7373gJeuceGlH0VG2QmBA0HqPlWLtj9kHJLQ,540616
cv2/data/haarcascade_frontalface_alt_tree.xml,sha256=Dl7kfswTJp1U3XpV-LU3UhZ8Ulh3IId3MjiPsHigSAo,2689040
cv2/data/haarcascade_frontalface_default.xml,sha256=D31FJ4ROtRTUpJSOgi2pD7sWo0oLu7xq3GSYdHpar7A,930127
cv2/data/haarcascade_fullbody.xml,sha256=BBdFxx7vG1yGrvIk8XznWwQtMzFMyPZ1dCT4vYzTCqE,476827
cv2/data/haarcascade_lefteye_2splits.xml,sha256=dMMjx4yBR1_JFY-sv7hmuwzKBr5B9XHfR9SsjQH5zkw,195369
cv2/data/haarcascade_license_plate_rus_16stages.xml,sha256=TRxEv3obxOIE-iWwRu0Kz_1_cTzBP-KVi2l3Elxg3eo,47775
cv2/data/haarcascade_lowerbody.xml,sha256=HmluHHxmxDmuIpz_-IcfQgN8NX6eHgkKK1nrwfj_XLs,395322
cv2/data/haarcascade_profileface.xml,sha256=s5pKO-RVOdsUan_B0-dhopLBluuIQhGF5qYVswVeYS0,828514
cv2/data/haarcascade_righteye_2splits.xml,sha256=TPDXK-pzB-mvfrmdSsvhXXEBpnwi_Nz77v1pKtN893Y,196170
cv2/data/haarcascade_russian_plate_number.xml,sha256=gUy1lUaCr1cOWDYfnl-LW1E6QRJ3a7nsrO-fDkymwtc,75482
cv2/data/haarcascade_smile.xml,sha256=TKHzBOq9C1rjAYDIGstT4Walhn5b4Xsxa9PzLP34fYo,188506
cv2/data/haarcascade_upperbody.xml,sha256=cyirT9sVkvU9mNfqWxudkOAa9dlfISrzeMfrV5BIu18,785819
cv2/detail/__init__.pyi,sha256=5YAN8RU88bFi0ANavonQnEeGpQaoR4XWyrAo8C3AFuA,22974
cv2/dnn/__init__.pyi,sha256=6rdBdBxevYLqp9DYxX6Kd3bKoFHPI2cqC-ANBZfI3gc,23647
cv2/fisheye/__init__.pyi,sha256=_yXNOowvjPflSnkcpV_8d1hYgScR8A2fAzmM3p4Kcy8,10019
cv2/flann/__init__.pyi,sha256=76rbelMvJhD-DlSPL4X6iMCrDUA4gJU3u89wAIwv6dk,2741
cv2/gapi/__init__.py,sha256=dPX9KhQqMbCkcHtwwL42N_D7-KlA7sQ3Lnuoflpc7bg,10621
cv2/gapi/__init__.pyi,sha256=xYmFoArWh9r_yxGCbn7HNzB7Cd680FCE5djtYKoenUM,14985
cv2/gapi/__pycache__/__init__.cpython-312.pyc,,
cv2/gapi/core/__init__.pyi,sha256=wptxRhi8QTCVVtvbGxEfEfW2-bzxJnBfrAMkBkkoWiQ,149
cv2/gapi/core/cpu/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/core/fluid/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/core/ocl/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/ie/__init__.pyi,sha256=fNj1r0wRnD__IX9kxOmonIPK0rrjoSsVXl9jVG8YaKA,1168
cv2/gapi/ie/detail/__init__.pyi,sha256=eIK6gjueMN8ZwQax5Kx16b46EjcZGZ3vFTkJvltw2E8,281
cv2/gapi/imgproc/__init__.pyi,sha256=zEBFwED_Pb4i-A70WA7afLjDUCT25hldcLqyIf7aIlc,76
cv2/gapi/imgproc/fluid/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/oak/__init__.pyi,sha256=KUOTM-AapO0L1tC4XF3gIYR8tmjmrFfh1Ro0jPzOB7g,1771
cv2/gapi/onnx/__init__.pyi,sha256=G71Ziv27W40XNei0BfQ_e3LutEKDfNBYUZVvBnOPJEQ,1552
cv2/gapi/onnx/ep/__init__.pyi,sha256=kH3LeGjjcdDXYOmBAJTyuDF8UpPLn5RQfmtk6DDt3QE,1420
cv2/gapi/ot/__init__.pyi,sha256=ds6r1REe-XpklhW2Cp4D3wdUUeh32SG7lnFBD_V7_ec,752
cv2/gapi/ot/cpu/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/ov/__init__.pyi,sha256=YGNlLikNzXNvpTSmyCmoT61hdV-HMAzz0hhyQtoC84E,2721
cv2/gapi/own/__init__.pyi,sha256=UhhiCQzT0I9USJnPYuniZwS-jpZz9R_ZoVc2o4XLAc8,74
cv2/gapi/own/detail/__init__.pyi,sha256=DFf4U7InxSpaY3zsrQsBxbxirU_KRRBWxqZYnhBUeqA,150
cv2/gapi/render/__init__.pyi,sha256=tGz4zgSK_JHLfRXydFfO7_Q-halDGTYzZYE2VU9tSsc,71
cv2/gapi/render/ocv/__init__.pyi,sha256=TjQnus2HhRKbZksmRWx8CjEZqLoXuKXILBF3vixp_XI,102
cv2/gapi/streaming/__init__.pyi,sha256=tTY9UO8_OIpoeMwKM-2IJu6shwY5JQ0QsD-sMvWE8es,855
cv2/gapi/video/__init__.pyi,sha256=byBGGnlpcEpg9Uvkiuogs29zn7Ettu7a54DQ5sTbXxg,160
cv2/gapi/wip/__init__.pyi,sha256=2tPCiodQeKqsW30msJTQIWZuG582wVNo9g45cq8_G3o,1127
cv2/gapi/wip/draw/__init__.pyi,sha256=wr-aOE4cPg3-DhASW1VSd9W8Jz9gcyc7wTU18lTzadA,3281
cv2/gapi/wip/gst/__init__.pyi,sha256=xnEGuDNceIX6TV3gwtoa_8MufhN8K3I_wl8Nli60HvQ,484
cv2/gapi/wip/onevpl/__init__.pyi,sha256=6pFrmrGjjqy16UWfP5jsCs_pcFXM4IkrmS_IHJ_LyE0,413
cv2/ipp/__init__.pyi,sha256=nuM46LgRNAVzwz_N17ekKzM-UWYiMl6f0WvMT6YwROo,237
cv2/load_config_py2.py,sha256=e0zdTYwgVMiD16RafBWr7PRov5r8IDkfHs5p6dGLSJc,157
cv2/load_config_py3.py,sha256=_1g6WHS-j4SOc8L2GzpxaAmVkmR5ybxDbmVlxcznygc,271
cv2/mat_wrapper/__init__.py,sha256=xEcH6hx281UYrlcrbBmJ12wq2n6FBDLkGAXf4RLU4wY,1164
cv2/mat_wrapper/__pycache__/__init__.cpython-312.pyc,,
cv2/misc/__init__.py,sha256=SVvXlZTM4XRnPjcshcTdj0_98rOnP9RiOVWw1V3g1GI,38
cv2/misc/__pycache__/__init__.cpython-312.pyc,,
cv2/misc/__pycache__/version.cpython-312.pyc,,
cv2/misc/version.py,sha256=yTpBh5P8sVubQxbAdBuDNnQOSQ6U87fR6-jNX28jgVw,95
cv2/ml/__init__.pyi,sha256=80LEjHnLHhPKI8wOyjiLk14WHl7oCgQ9xAwXCLP6YxE,23498
cv2/ocl/__init__.pyi,sha256=21xbasu56BrLPuqkfeIAVe1gCWByzg4ngBL5Kc4ETnA,5779
cv2/ogl/__init__.pyi,sha256=BM0glpRfs1M6bDFiTHdHSaUFAaRTozkJNxNXvBkvcps,1523
cv2/opencv_videoio_ffmpeg4120_64.dll,sha256=oPAeTuXpe0pRPNcPAfr63A3Rh7pdEpPLf8a3fn0XxjE,28349440
cv2/parallel/__init__.pyi,sha256=PyChkEzYlrHr5UsgQeh9Fh8E43XjURc0uY8It3IHJ3c,135
cv2/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cv2/samples/__init__.pyi,sha256=HnrSW6_dgL9sYkyCZ2qx2SoLNrA05oaI4tCSS4i2TOQ,336
cv2/segmentation/__init__.pyi,sha256=lvZlHkp75KCijtkNZu3HkOmH9_pN6emzFZ0e421bJ2I,1778
cv2/typing/__init__.py,sha256=R1zrlV0jFYk08Araeo8zrz2tTxcS_RPjLMeagsHrfUo,5545
cv2/typing/__pycache__/__init__.cpython-312.pyc,,
cv2/utils/__init__.py,sha256=KxaZCzW1aa8cpyOdwQ97JOxi8npGYmseLxJx0uGqNVQ,344
cv2/utils/__init__.pyi,sha256=A2n4iAX8yr1EA1fOuGdKzIE39uM1gIMbRvlzW-DPZuk,3701
cv2/utils/__pycache__/__init__.cpython-312.pyc,,
cv2/utils/fs/__init__.pyi,sha256=BPwL654636kP4k95U4QPp7oMZcgJ2QDIYrb9F8h4c7I,93
cv2/utils/nested/__init__.pyi,sha256=u3osqQeekndY9_-xxK1PAD44dXZaGLYhyfeFYbV4npA,604
cv2/version.py,sha256=np1TgrH3Q_BtbhKs4wXUB4ZDL_Uu76Es8XHPlxVLu00,97
cv2/videoio_registry/__init__.pyi,sha256=_ZZH2FSYJNuOWgDSLTTfUMkycnYYzXZufjyg9HmlQNw,993
opencv_python-4.12.0.88.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
opencv_python-4.12.0.88.dist-info/LICENSE-3RD-PARTY.txt,sha256=2OyIgyD8udmTF6d69KSjqRIIZ2Bn7B-pvBlnpSJBFzA,177945
opencv_python-4.12.0.88.dist-info/LICENSE.txt,sha256=7e8PrB6wjSnTRWP3JHQuB42iUT4ZYTOhLGrZ_wHiYQc,1090
opencv_python-4.12.0.88.dist-info/METADATA,sha256=Yq7cGLI0v_M8B-zFe62y8QYZLcMJxiCCpsX2fJtP_cw,19998
opencv_python-4.12.0.88.dist-info/RECORD,,
opencv_python-4.12.0.88.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opencv_python-4.12.0.88.dist-info/WHEEL,sha256=9fG10vSzOKXwmjX3CkWN62P9hqj-GGl2wzwdRxOVR-Y,94
opencv_python-4.12.0.88.dist-info/top_level.txt,sha256=SY8vrf_sYOg99OP9euhz7q36pPy_2VK5vbeEWXwwSoc,4
