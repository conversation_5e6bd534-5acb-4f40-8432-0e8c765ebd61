# Tesseract OCR 安裝指南

## Windows 系統安裝步驟

### 方法一：自動下載安裝（推薦）

1. 前往 Tesseract OCR 官方下載頁面：
   https://github.com/UB-Mannheim/tesseract/wiki

2. 下載最新版本的 Windows 安裝程式：
   - 選擇 `tesseract-ocr-w64-setup-5.x.x.xxxxxxxx.exe`（64位元系統）
   - 或 `tesseract-ocr-w32-setup-5.x.x.xxxxxxxx.exe`（32位元系統）

3. 執行安裝程式：
   - 選擇安裝路徑（建議使用預設路徑：`C:\Program Files\Tesseract-OCR`）
   - **重要：在語言選擇頁面，確保勾選「Chinese (Traditional)」**
   - 完成安裝

### 方法二：使用 Chocolatey（進階用戶）

如果您已安裝 Chocolatey 套件管理器：

```powershell
# 以管理員身份執行 PowerShell
choco install tesseract

# 安裝繁體中文語言包
# 注意：可能需要手動下載語言包
```

### 方法三：手動安裝

1. 下載 Tesseract 執行檔
2. 下載繁體中文語言包：
   - 前往：https://github.com/tesseract-ocr/tessdata
   - 下載 `chi_tra.traineddata`
   - 將檔案放入 Tesseract 安裝目錄的 `tessdata` 資料夾

## 驗證安裝

安裝完成後，開啟命令提示字元或 PowerShell，執行：

```bash
tesseract --version
tesseract --list-langs
```

您應該看到：
- Tesseract 版本資訊
- 語言列表中包含 `chi_tra`（繁體中文）

## 常見問題

### 1. 找不到 tesseract 命令
- 確認 Tesseract 已加入系統 PATH 環境變數
- 重新啟動命令提示字元或 PowerShell
- 如果仍有問題，手動將 Tesseract 安裝路徑加入 PATH

### 2. 沒有繁體中文語言包
- 重新執行安裝程式，確保選擇了 Chinese (Traditional)
- 或手動下載 `chi_tra.traineddata` 到 tessdata 資料夾

### 3. 權限問題
- 以管理員身份執行安裝程式
- 確認安裝路徑有寫入權限

## 測試安裝

安裝完成後，執行我們的測試腳本：

```bash
# 啟動虛擬環境
pdf_ocr_env\Scripts\activate

# 執行測試
python test_ocr.py
```

如果看到「✓ 支援繁體中文」，表示安裝成功！
