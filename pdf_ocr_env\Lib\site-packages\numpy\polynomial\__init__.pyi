from typing import Final, Literal

from .polynomial import Polynomial
from .chebyshev import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .legendre import <PERSON><PERSON>
from .hermite import Hermite
from .hermite_e import Hermite<PERSON>
from .laguerre import <PERSON>guerre
from . import polynomial, chebyshev, legendre, hermite, hermite_e, laguerre

__all__ = [
    "set_default_printstyle",
    "polynomial", "Polynomial",
    "chebyshev", "Chebyshev",
    "legendre", "Legendre",
    "hermite", "Hermite",
    "hermite_e", "HermiteE",
    "laguerre", "Laguerre",
]

def set_default_printstyle(style: Literal["ascii", "unicode"]) -> None: ...

from numpy._pytesttester import PytestTester as _PytestTester
test: Final[_PytestTester]
