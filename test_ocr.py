#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF OCR 測試腳本
用於測試 OCR 功能是否正常運作
"""

import os
from pdf_ocr import PDFOCRProcessor


def test_tesseract_installation():
    """測試 Tesseract 是否正確安裝"""
    print("=== 測試 Tesseract 安裝 ===")
    
    try:
        import pytesseract
        
        # 測試 Tesseract 版本
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract 版本：{version}")
        
        # 測試支援的語言
        languages = pytesseract.get_languages()
        print(f"✓ 支援的語言：{', '.join(languages)}")
        
        # 檢查是否支援繁體中文
        if 'chi_tra' in languages:
            print("✓ 支援繁體中文")
        else:
            print("✗ 不支援繁體中文，請安裝繁體中文語言包")
        
        return True
        
    except Exception as e:
        print(f"✗ Tesseract 測試失敗：{str(e)}")
        return False


def test_image_processing():
    """測試圖像處理功能"""
    print("\n=== 測試圖像處理功能 ===")
    
    try:
        from PIL import Image
        import cv2
        import numpy as np
        
        # 創建一個測試圖像
        test_image = Image.new('RGB', (200, 100), color='white')
        print("✓ PIL 圖像創建成功")
        
        # 測試 OpenCV 轉換
        opencv_image = cv2.cvtColor(np.array(test_image), cv2.COLOR_RGB2BGR)
        print("✓ OpenCV 轉換成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 圖像處理測試失敗：{str(e)}")
        return False


def test_pdf_processing():
    """測試 PDF 處理功能"""
    print("\n=== 測試 PDF 處理功能 ===")
    
    try:
        import fitz
        
        # 測試 PyMuPDF
        print(f"✓ PyMuPDF 版本：{fitz.version}")
        
        return True
        
    except Exception as e:
        print(f"✗ PDF 處理測試失敗：{str(e)}")
        return False


def create_sample_pdf():
    """創建一個簡單的測試 PDF"""
    print("\n=== 創建測試 PDF ===")
    
    try:
        import fitz
        
        # 創建新的 PDF 文檔
        doc = fitz.open()
        page = doc.new_page()
        
        # 添加一些測試文字
        text = """測試文檔
        
這是一個測試 PDF 文檔，用於驗證 OCR 功能。

Test Document

This is a test PDF document for OCR verification.

包含中文和英文內容。
Contains both Chinese and English content.
        """
        
        # 插入文字
        page.insert_text((50, 50), text, fontsize=12)
        
        # 儲存 PDF
        doc.save("test_sample.pdf")
        doc.close()
        
        print("✓ 測試 PDF 創建成功：test_sample.pdf")
        return True
        
    except Exception as e:
        print(f"✗ 創建測試 PDF 失敗：{str(e)}")
        return False


def run_ocr_test():
    """執行完整的 OCR 測試"""
    print("\n=== 執行 OCR 測試 ===")
    
    # 檢查是否有測試 PDF
    if not os.path.exists("test_sample.pdf"):
        print("找不到測試 PDF，正在創建...")
        if not create_sample_pdf():
            return False
    
    try:
        # 創建 OCR 處理器
        processor = PDFOCRProcessor()
        
        # 處理測試 PDF
        print("正在處理測試 PDF...")
        text = processor.process_pdf("test_sample.pdf", "test_output.txt")
        
        if text:
            print("✓ OCR 處理成功")
            print(f"提取的文字長度：{len(text)} 字元")
            print("文字預覽：")
            print("-" * 30)
            print(text[:200] + "..." if len(text) > 200 else text)
            return True
        else:
            print("✗ OCR 處理失敗，未提取到文字")
            return False
            
    except Exception as e:
        print(f"✗ OCR 測試失敗：{str(e)}")
        return False


def main():
    """主測試函數"""
    print("PDF OCR 系統測試")
    print("=" * 50)
    
    tests = [
        ("Tesseract 安裝", test_tesseract_installation),
        ("圖像處理功能", test_image_processing),
        ("PDF 處理功能", test_pdf_processing),
        ("OCR 完整測試", run_ocr_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n執行測試：{test_name}")
        if test_func():
            passed += 1
        else:
            print(f"測試失敗：{test_name}")
    
    print("\n" + "=" * 50)
    print(f"測試結果：{passed}/{total} 通過")
    
    if passed == total:
        print("✓ 所有測試通過！系統已準備就緒。")
    else:
        print("✗ 部分測試失敗，請檢查安裝和設定。")


if __name__ == "__main__":
    main()
