#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF OCR 處理程式
使用 PyMuPDF、Pillow、pytesseract 和 OpenCV 來處理 PDF 文檔的 OCR
"""

import fitz  # PyMuPDF
from PIL import Image
import pytesseract
import cv2
import numpy as np
import os
import sys
import io
from pathlib import Path


class PDFOCRProcessor:
    """PDF OCR 處理器類別"""
    
    def __init__(self, tesseract_path=None):
        """
        初始化 PDF OCR 處理器

        Args:
            tesseract_path (str, optional): Tesseract 執行檔路徑
        """
        # 設定 Tesseract 路徑（Windows 系統可能需要）
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            print(f"使用指定的 Tesseract 路徑：{tesseract_path}")
        elif os.name == 'nt':  # Windows 系統
            # 常見的 Tesseract 安裝路徑
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME')),
                r'C:\Tesseract-OCR\tesseract.exe'
            ]
            tesseract_found = False
            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"找到 Tesseract：{path}")
                    tesseract_found = True
                    break

            if not tesseract_found:
                print("警告：未找到 Tesseract 安裝路徑，請確認已正確安裝 Tesseract OCR")
                print("或手動指定路徑：PDFOCRProcessor(tesseract_path='您的路徑')")
    
    def pdf_to_images(self, pdf_path, dpi=300):
        """
        將 PDF 文件轉換為圖像
        
        Args:
            pdf_path (str): PDF 文件路徑
            dpi (int): 解析度，預設 300 DPI
            
        Returns:
            list: PIL Image 物件列表
        """
        try:
            # 開啟 PDF 文件
            pdf_document = fitz.open(pdf_path)
            images = []
            
            print(f"正在處理 PDF 文件：{pdf_path}")
            print(f"總頁數：{len(pdf_document)}")
            
            # 遍歷每一頁
            for page_num in range(len(pdf_document)):
                print(f"正在轉換第 {page_num + 1} 頁...")
                
                # 獲取頁面
                page = pdf_document.load_page(page_num)
                
                # 設定轉換矩陣（控制解析度）
                mat = fitz.Matrix(dpi/72, dpi/72)
                
                # 將頁面轉換為圖像
                pix = page.get_pixmap(matrix=mat)
                
                # 轉換為 PIL Image
                img_data = pix.tobytes("ppm")
                img = Image.open(io.BytesIO(img_data))
                images.append(img)
            
            pdf_document.close()
            return images
            
        except Exception as e:
            print(f"PDF 轉換錯誤：{str(e)}")
            return []
    
    def preprocess_image(self, image, enhance_for_chinese=True):
        """
        預處理圖像以提高 OCR 準確性

        Args:
            image (PIL.Image): 輸入圖像
            enhance_for_chinese (bool): 是否針對中文進行增強處理

        Returns:
            PIL.Image: 預處理後的圖像
        """
        # 轉換為 OpenCV 格式
        opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # 轉換為灰階
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)

        if enhance_for_chinese:
            # 針對中文字符的增強處理

            # 1. 高斯模糊降噪
            blurred = cv2.GaussianBlur(gray, (1, 1), 0)

            # 2. 對比度增強
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(blurred)

            # 3. 自適應二值化（對中文字符效果更好）
            binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)

            # 4. 形態學操作去除噪點
            kernel = np.ones((1,1), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        else:
            # 標準處理
            denoised = cv2.medianBlur(gray, 3)
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 轉換回 PIL Image
        processed_image = Image.fromarray(binary)

        return processed_image
    
    def perform_ocr(self, image, lang='chi_tra+eng'):
        """
        對圖像執行 OCR

        Args:
            image (PIL.Image): 輸入圖像
            lang (str): OCR 語言，預設為繁體中文+英文

        Returns:
            str: 提取的文字
        """
        try:
            # 設定 OCR 配置參數以提高中文識別準確性
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十百千萬億兆京垓秭穰溝澗正載極恆河沙阿僧祇那由他不可思議無量大數'

            # 先嘗試使用自訂配置
            try:
                text = pytesseract.image_to_string(image, lang=lang, config=custom_config)
                if text.strip():
                    return text.strip()
            except:
                pass

            # 如果自訂配置失敗，使用預設配置
            text = pytesseract.image_to_string(image, lang=lang, config=r'--oem 3 --psm 6')
            return text.strip()
        except Exception as e:
            print(f"OCR 處理錯誤：{str(e)}")
            return ""
    
    def process_pdf(self, pdf_path, output_path=None, dpi=300, lang='chi_tra+eng'):
        """
        處理整個 PDF 文件
        
        Args:
            pdf_path (str): PDF 文件路徑
            output_path (str, optional): 輸出文字文件路徑
            dpi (int): 解析度
            lang (str): OCR 語言
            
        Returns:
            str: 提取的所有文字
        """
        # 將 PDF 轉換為圖像
        images = self.pdf_to_images(pdf_path, dpi)
        
        if not images:
            print("無法轉換 PDF 文件")
            return ""
        
        all_text = []
        
        # 處理每一頁
        for i, image in enumerate(images):
            print(f"正在進行 OCR 處理第 {i + 1} 頁...")

            # 預處理圖像（針對中文優化）
            processed_image = self.preprocess_image(image, enhance_for_chinese=True)

            # 執行 OCR
            text = self.perform_ocr(processed_image, lang)

            if text:
                all_text.append(f"=== 第 {i + 1} 頁 ===\n{text}\n")
            else:
                all_text.append(f"=== 第 {i + 1} 頁 ===\n[無法識別文字]\n")
        
        # 合併所有文字
        final_text = "\n".join(all_text)
        
        # 儲存到文字文件
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(final_text)
                print(f"文字已儲存到：{output_path}")
            except Exception as e:
                print(f"儲存文件錯誤：{str(e)}")
        
        return final_text


def main():
    """主程式"""
    if len(sys.argv) < 2:
        print("使用方法：python pdf_ocr.py <PDF文件路徑> [輸出文字文件路徑]")
        print("範例：python pdf_ocr.py document.pdf output.txt")
        return
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 檢查 PDF 文件是否存在
    if not os.path.exists(pdf_path):
        print(f"錯誤：找不到 PDF 文件 {pdf_path}")
        return
    
    # 如果沒有指定輸出路徑，自動生成
    if not output_path:
        pdf_name = Path(pdf_path).stem
        output_path = f"{pdf_name}_ocr.txt"
    
    # 創建 OCR 處理器
    processor = PDFOCRProcessor()
    
    # 處理 PDF
    print("開始處理 PDF OCR...")
    text = processor.process_pdf(pdf_path, output_path)
    
    if text:
        print("OCR 處理完成！")
        print(f"提取的文字預覽（前 200 字元）：")
        print("-" * 50)
        print(text[:200] + "..." if len(text) > 200 else text)
    else:
        print("OCR 處理失敗或未提取到文字")


if __name__ == "__main__":
    main()
