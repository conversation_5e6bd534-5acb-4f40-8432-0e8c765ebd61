#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 PaddleOCR 的基本功能
"""

from paddleocr import PaddleOCR
import cv2
import numpy as np
from PIL import Image

def test_paddleocr():
    """測試 PaddleOCR 基本功能"""
    print("正在初始化 PaddleOCR...")
    
    try:
        # 初始化 PaddleOCR
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
        print("✓ PaddleOCR 初始化成功")

        # 檢查是否有測試圖像
        import os
        if os.path.exists("chinese_test.pdf"):
            # 使用 PyMuPDF 轉換 PDF 第一頁為圖像
            import fitz
            import io

            pdf_document = fitz.open("chinese_test.pdf")
            page = pdf_document.load_page(0)
            mat = fitz.Matrix(300/72, 300/72)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("ppm")
            img = Image.open(io.BytesIO(img_data))

            # 轉換為 numpy array
            img_array = np.array(img)

            print("正在執行 OCR...")
            result = ocr.ocr(img_array)
            
            print("OCR 結果：")
            print(f"結果類型：{type(result)}")
            print(f"結果內容：{result}")
            
            if result and result[0]:
                print("\n解析的文字：")
                for line in result[0]:
                    print(f"行：{line}")
                    if len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        print(f"文字：{text}, 置信度：{confidence}")
            
            pdf_document.close()
        else:
            print("找不到測試 PDF 文件")
            
    except Exception as e:
        print(f"錯誤：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_paddleocr()
