#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF OCR 處理程式 - PaddleOCR 版本
使用 PyMuPDF、Pillow 和 PaddleOCR 來處理 PDF 文檔的 OCR
"""

import fitz  # PyMuPDF
from PIL import Image
import cv2
import numpy as np
import os
import sys
import io
from pathlib import Path
from paddleocr import PaddleOCR


class PDFOCRPaddleProcessor:
    """PDF OCR 處理器類別 - 使用 PaddleOCR"""
    
    def __init__(self, use_textline_orientation=True, lang='ch'):
        """
        初始化 PDF OCR 處理器

        Args:
            use_textline_orientation (bool): 是否使用文字方向分類器
            lang (str): 語言設定，'ch' 為中文，'en' 為英文
        """
        print("正在初始化 PaddleOCR...")
        try:
            # 初始化 PaddleOCR
            # use_textline_orientation=True 可以處理旋轉的文字
            # lang='ch' 支援中文識別
            self.ocr = PaddleOCR(use_textline_orientation=use_textline_orientation, lang=lang)
            print("✓ PaddleOCR 初始化成功")
        except Exception as e:
            print(f"✗ PaddleOCR 初始化失敗：{str(e)}")
            raise
    
    def pdf_to_images(self, pdf_path, dpi=300):
        """
        將 PDF 文件轉換為圖像
        
        Args:
            pdf_path (str): PDF 文件路徑
            dpi (int): 解析度，預設 300 DPI
            
        Returns:
            list: PIL Image 物件列表
        """
        try:
            # 開啟 PDF 文件
            pdf_document = fitz.open(pdf_path)
            images = []
            
            print(f"正在處理 PDF 文件：{pdf_path}")
            print(f"總頁數：{len(pdf_document)}")
            
            # 遍歷每一頁
            for page_num in range(len(pdf_document)):
                print(f"正在轉換第 {page_num + 1} 頁...")
                
                # 獲取頁面
                page = pdf_document.load_page(page_num)
                
                # 設定轉換矩陣（控制解析度）
                mat = fitz.Matrix(dpi/72, dpi/72)
                
                # 將頁面轉換為圖像
                pix = page.get_pixmap(matrix=mat)
                
                # 轉換為 PIL Image
                img_data = pix.tobytes("ppm")
                img = Image.open(io.BytesIO(img_data))
                images.append(img)
            
            pdf_document.close()
            return images
            
        except Exception as e:
            print(f"PDF 轉換錯誤：{str(e)}")
            return []
    
    def preprocess_image(self, image):
        """
        預處理圖像以提高 OCR 準確性
        
        Args:
            image (PIL.Image): 輸入圖像
            
        Returns:
            numpy.ndarray: 預處理後的圖像（OpenCV 格式）
        """
        # 轉換為 OpenCV 格式
        opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # PaddleOCR 通常對原始圖像效果較好，但我們可以做一些基本的增強
        # 轉換為灰階
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
        
        # 輕微的高斯模糊去噪
        denoised = cv2.GaussianBlur(gray, (1, 1), 0)
        
        # 對比度增強
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # 轉換回 BGR 格式（PaddleOCR 需要）
        processed_image = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
        
        return processed_image
    
    def perform_ocr(self, image):
        """
        對圖像執行 OCR

        Args:
            image (numpy.ndarray): 輸入圖像（OpenCV 格式）

        Returns:
            str: 提取的文字
        """
        try:
            # 執行 OCR
            result = self.ocr.ocr(image)

            # 解析結果 - 新版本的 PaddleOCR 結果格式
            text_lines = []
            if result and isinstance(result, list) and len(result) > 0:
                # 結果是一個列表，第一個元素包含所有識別結果
                ocr_result = result[0]

                # 檢查是否有 rec_texts 和 rec_scores
                if isinstance(ocr_result, dict):
                    if 'rec_texts' in ocr_result and 'rec_scores' in ocr_result:
                        texts = ocr_result['rec_texts']
                        scores = ocr_result['rec_scores']

                        for text, score in zip(texts, scores):
                            # 只保留置信度較高的文字
                            if score > 0.5:
                                text_lines.append(text)
                    elif 'rec_text' in ocr_result:
                        # 另一種可能的格式
                        for item in ocr_result['rec_text']:
                            if isinstance(item, str):
                                text_lines.append(item)

            return '\n'.join(text_lines)

        except Exception as e:
            print(f"OCR 處理錯誤：{str(e)}")
            return ""
    
    def process_pdf(self, pdf_path, output_path=None, dpi=300):
        """
        處理整個 PDF 文件
        
        Args:
            pdf_path (str): PDF 文件路徑
            output_path (str, optional): 輸出文字文件路徑
            dpi (int): 解析度
            
        Returns:
            str: 提取的所有文字
        """
        # 將 PDF 轉換為圖像
        images = self.pdf_to_images(pdf_path, dpi)
        
        if not images:
            print("無法轉換 PDF 文件")
            return ""
        
        all_text = []
        
        # 處理每一頁
        for i, image in enumerate(images):
            print(f"正在進行 OCR 處理第 {i + 1} 頁...")
            
            # 預處理圖像
            processed_image = self.preprocess_image(image)
            
            # 執行 OCR
            text = self.perform_ocr(processed_image)
            
            if text:
                all_text.append(f"=== 第 {i + 1} 頁 ===\n{text}\n")
            else:
                all_text.append(f"=== 第 {i + 1} 頁 ===\n[無法識別文字]\n")
        
        # 合併所有文字
        final_text = "\n".join(all_text)
        
        # 儲存到文字文件
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(final_text)
                print(f"文字已儲存到：{output_path}")
            except Exception as e:
                print(f"儲存文件錯誤：{str(e)}")
        
        return final_text


def main():
    """主程式"""
    if len(sys.argv) < 2:
        print("使用方法：python pdf_ocr_paddle.py <PDF文件路徑> [輸出文字文件路徑]")
        print("範例：python pdf_ocr_paddle.py document.pdf output.txt")
        return
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 檢查 PDF 文件是否存在
    if not os.path.exists(pdf_path):
        print(f"錯誤：找不到 PDF 文件 {pdf_path}")
        return
    
    # 如果沒有指定輸出路徑，自動生成
    if not output_path:
        pdf_name = Path(pdf_path).stem
        output_path = f"{pdf_name}_paddle_ocr.txt"
    
    # 創建 OCR 處理器
    try:
        processor = PDFOCRPaddleProcessor(use_textline_orientation=True, lang='ch')
    except Exception as e:
        print(f"無法初始化 PaddleOCR：{str(e)}")
        return
    
    # 處理 PDF
    print("開始處理 PDF OCR...")
    text = processor.process_pdf(pdf_path, output_path)
    
    if text:
        print("OCR 處理完成！")
        print(f"提取的文字預覽（前 200 字元）：")
        print("-" * 50)
        print(text[:200] + "..." if len(text) > 200 else text)
    else:
        print("OCR 處理失敗或未提取到文字")


if __name__ == "__main__":
    main()
